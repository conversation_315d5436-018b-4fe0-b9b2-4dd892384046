#include "flash_app.h"

#define CONFIG_FLASH_ADDR       0x000000
#define CYCLE_FLASH_ADDR        0x001000
#define POWER_FLASH_ADDR        0x002000
#define DEVICE_ID_FLASH_ADDR    0x003000

void Flash_WriteConfig(void)
{
    spi_flash_sector_erase(CONFIG_FLASH_ADDR);
    spi_flash_wait_for_write_end();
    spi_flash_buffer_write((uint8_t *)&config_data, CONFIG_FLASH_ADDR, sizeof(config_data));
}

void Flash_ReadConfig(void)
{
    spi_flash_buffer_read((uint8_t *)&config_data, CONFIG_FLASH_ADDR, sizeof(config_data));
}

void Flash_WriteCycle(void)
{
    spi_flash_sector_erase(CYCLE_FLASH_ADDR);
    spi_flash_wait_for_write_end();
    spi_flash_buffer_write((uint8_t *)&sample_cycle, CYCLE_FLASH_ADDR, sizeof(sample_cycle));
}

void Flash_ReadCycle(void)
{
    spi_flash_buffer_read((uint8_t *)&sample_cycle, CYCLE_FLASH_ADDR, sizeof(sample_cycle));
}

void Flash_WritePower(void)
{
    spi_flash_sector_erase(POWER_FLASH_ADDR);
    spi_flash_wait_for_write_end();
    spi_flash_buffer_write((uint8_t *)&power_count, POWER_FLASH_ADDR, sizeof(power_count));
}

void Flash_ReadPower(void)
{
    spi_flash_buffer_read((uint8_t *)&power_count, POWER_FLASH_ADDR, sizeof(power_count));
}

void Flash_WriteDeviceId(uint8_t *device_id)
{
    spi_flash_sector_erase(DEVICE_ID_FLASH_ADDR);
    spi_flash_wait_for_write_end();
    spi_flash_buffer_write(device_id, DEVICE_ID_FLASH_ADDR, 10);
}

void Flash_ReadDeviceId(void)
{
    spi_flash_buffer_read(device_id, DEVICE_ID_FLASH_ADDR, 10);
}
