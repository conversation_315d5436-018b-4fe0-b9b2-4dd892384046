{"name": "Project", "type": "ARM", "dependenceList": [], "srcDirs": [], "virtualFolder": {"name": "<virtual_root>", "files": [], "folders": [{"name": "Core", "files": [{"path": "Core/Src/main.c"}, {"path": "Core/Src/systick.c"}, {"path": "Core/Src/gd32f4xx_it.c"}], "folders": []}, {"name": "MyApps", "files": [], "folders": []}, {"name": "CMSIS", "files": [{"path": "Firmware/CMSIS/GD/GD32F4xx/Source/system_gd32f4xx.c"}, {"path": "Firmware/CMSIS/GD/GD32F4xx/Source/ARM/startup_gd32f450_470.s"}], "folders": []}, {"name": "Peripher<PERSON>", "files": [{"path": "Firmware/GD32F4xx_standard_peripheral/Source/gd32f4xx_adc.c"}, {"path": "Firmware/GD32F4xx_standard_peripheral/Source/gd32f4xx_can.c"}, {"path": "Firmware/GD32F4xx_standard_peripheral/Source/gd32f4xx_crc.c"}, {"path": "Firmware/GD32F4xx_standard_peripheral/Source/gd32f4xx_ctc.c"}, {"path": "Firmware/GD32F4xx_standard_peripheral/Source/gd32f4xx_dac.c"}, {"path": "Firmware/GD32F4xx_standard_peripheral/Source/gd32f4xx_dbg.c"}, {"path": "Firmware/GD32F4xx_standard_peripheral/Source/gd32f4xx_dci.c"}, {"path": "Firmware/GD32F4xx_standard_peripheral/Source/gd32f4xx_dma.c"}, {"path": "Firmware/GD32F4xx_standard_peripheral/Source/gd32f4xx_enet.c"}, {"path": "Firmware/GD32F4xx_standard_peripheral/Source/gd32f4xx_exmc.c"}, {"path": "Firmware/GD32F4xx_standard_peripheral/Source/gd32f4xx_exti.c"}, {"path": "Firmware/GD32F4xx_standard_peripheral/Source/gd32f4xx_fmc.c"}, {"path": "Firmware/GD32F4xx_standard_peripheral/Source/gd32f4xx_fwdgt.c"}, {"path": "Firmware/GD32F4xx_standard_peripheral/Source/gd32f4xx_gpio.c"}, {"path": "Firmware/GD32F4xx_standard_peripheral/Source/gd32f4xx_i2c.c"}, {"path": "Firmware/GD32F4xx_standard_peripheral/Source/gd32f4xx_ipa.c"}, {"path": "Firmware/GD32F4xx_standard_peripheral/Source/gd32f4xx_iref.c"}, {"path": "Firmware/GD32F4xx_standard_peripheral/Source/gd32f4xx_misc.c"}, {"path": "Firmware/GD32F4xx_standard_peripheral/Source/gd32f4xx_pmu.c"}, {"path": "Firmware/GD32F4xx_standard_peripheral/Source/gd32f4xx_rcu.c"}, {"path": "Firmware/GD32F4xx_standard_peripheral/Source/gd32f4xx_rtc.c"}, {"path": "Firmware/GD32F4xx_standard_peripheral/Source/gd32f4xx_sdio.c"}, {"path": "Firmware/GD32F4xx_standard_peripheral/Source/gd32f4xx_spi.c"}, {"path": "Firmware/GD32F4xx_standard_peripheral/Source/gd32f4xx_syscfg.c"}, {"path": "Firmware/GD32F4xx_standard_peripheral/Source/gd32f4xx_timer.c"}, {"path": "Firmware/GD32F4xx_standard_peripheral/Source/gd32f4xx_tli.c"}, {"path": "Firmware/GD32F4xx_standard_peripheral/Source/gd32f4xx_trng.c"}, {"path": "Firmware/GD32F4xx_standard_peripheral/Source/gd32f4xx_usart.c"}, {"path": "Firmware/GD32F4xx_standard_peripheral/Source/gd32f4xx_wwdgt.c"}], "folders": []}]}, "outDir": "build", "deviceName": null, "packDir": null, "miscInfo": {"uid": "9606ba91b3f0fa85bf97f92f4acd4b44"}, "targets": {"GD32F470VET6": {"excludeList": [], "toolchain": "AC5", "compileConfig": {"cpuType": "Cortex-M4", "archExtensions": "", "floatingPointHardware": "single", "scatterFilePath": "", "useCustomScatterFile": false, "storageLayout": {"RAM": [{"tag": "RAM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "IRAM", "id": 1, "mem": {"startAddr": "0x20000000", "size": "0x30000"}, "isChecked": true, "noInit": false}, {"tag": "IRAM", "id": 2, "mem": {"startAddr": "0x10000000", "size": "0x10000"}, "isChecked": false, "noInit": false}], "ROM": [{"tag": "ROM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "IROM", "id": 1, "mem": {"startAddr": "0x8000000", "size": "0x80000"}, "isChecked": true, "isStartup": true}, {"tag": "IROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}]}, "options": "null"}, "uploader": "JLink", "uploadConfig": {"bin": "", "baseAddr": "", "cpuInfo": {"vendor": "null", "cpuName": "null"}, "proType": 1, "speed": 8000, "otherCmds": ""}, "uploadConfigMap": {}, "custom_dep": {"name": "default", "incList": ["Core/Inc", "sysFunction/Inc", "Firmware/CMSIS", "Firmware/CMSIS/GD/GD32F4xx/Include", "Firmware/GD32F4xx_standard_peripheral/Include", ".cmsis/include", "MDK-ARM/RTE/_GD32F470VET6", "Compenents/Btn/Inc", "Compenents/Oled/Inc", "Compenents/Gd25qxx/Inc", "Compenents/Fatfs/Inc", "Compenents/Sdio/Inc"], "libList": [], "defineList": []}, "builderOptions": {"AC5": {"version": 4, "beforeBuildTasks": [], "afterBuildTasks": [], "global": {"use-microLIB": true, "output-debug-info": "enable"}, "c/cpp-compiler": {"optimization": "level-1", "one-elf-section-per-function": true, "c99-mode": true, "C_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "CXX_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "warnings": "all-warnings", "gnu-extensions": true}, "asm-compiler": {}, "linker": {"$outputTaskExcludes": [".bin"], "output-format": "elf", "xo-base": "", "ro-base": "0x08000000", "rw-base": "0x20000000"}}}}}, "version": "3.6"}