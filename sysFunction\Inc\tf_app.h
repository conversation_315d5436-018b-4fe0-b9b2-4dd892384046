#ifndef __TF_APP_H__
#define __TF_APP_H__

#include "gd32f470vet6_bsp.h"

typedef enum
{
    LOG_SYSTEM_INIT = 0,
    LOG_RTC_CONFIG,
    LOG_RTC_CONFIG_OK,
    LOG_SYSTEM_TEST,
    LOG_TEST_OK,
    LOG_FLASH_ERROR,
    LOG_TFCARD_ERROR,
    LOG_RATIO_CONFIG,
    LOG_RATIO_CONFIG_OK,
    LOG_LIMIT_CONFIG,
    LOG_LIMIT_CONFIG_OK,
    LOG_SAMPLE_START_USART,
    LOG_SAMPLE_START_BTN,
    LOG_CYCLE_CONFIG,
    LOG_SAMPLE_STOP_USART,
    LOG_SAMPLE_STOP_BTN,
    LOG_HIDE_DATA,
    LOG_UNHIDE_DATA
} LogType_t;

ErrStatus TF_WriteRatio(float ch0, float ch1, float ch2);
void TF_WriteSample(void);
void TF_WriteOverLimit(void);
void TF_WriteHideData(void);
void TF_WriteLog(LogType_t log_type);

#endif  /* __TF_APP_H__ */
