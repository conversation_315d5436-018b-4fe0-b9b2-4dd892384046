#include "usart_app.h"

typedef enum
{
    OTHER_CMD = 0,
    RATIO_CONFIG,
    LIMIT_CONFIG,
    DEVICE_ID_CONFIG
} UsartStatus_t;

UsartStatus_t usart_status = OTHER_CMD;

void Cmd_GetDeviceId(void)
{
    Usart1Printf("report:device_id=%s\r\n", device_id);
}

void Cmd_DeviceIdConfig(char *buffer)
{
    uint8_t input_buffer[32] = {0};
    int chars_read;

    int result = sscanf(buffer, "%s\n%n", input_buffer, &chars_read);
    if (result == 1 && chars_read == strlen(buffer))
    {
        Flash_WriteDeviceId(input_buffer);

        Usart1Printf("device_id modified success\n");
        Usart1Printf("device_id: %s\n", input_buffer);
    }
}

void Cmd_Hide(void)
{
    sample_status.hide = 1;
}

void Cmd_UnHide(void)
{
    sample_status.hide = 0;
}

void Cmd_PowerReset(void)
{
    power_count = 0;
    Flash_WritePower();
    Usart1Printf("Power reset success!\n");
}

void Cmd_Start(void)
{
    Usart1Printf("Periodic Sampling\n");
    Usart1Printf("sample cycle: %ds\n", sample_cycle);
    sample_status.enable = 1;
    OLED_Clear();
    SampleProc();
}

void Cmd_Stop(void)
{
    Usart1Printf("Periodic Sampling STOP\n");

    sample_status.enable = 0;

    OLED_Clear();
    OledDrawStr(0, 0, "system idle");
}

static void Cmd_ConfigSave(void)
{
    Usart1Printf("ratio: %.2f\n", config_data[0].ratio);
    Usart1Printf("limit: %.2f\n", config_data[0].limit);

    Flash_WriteConfig();
    Usart1Printf("save parameters to flash\n");
}

static void Cmd_ConfigRead(void)
{
    Flash_ReadConfig();
    Usart1Printf("read parameters from flash\n");
    Usart1Printf("ratio: %.2f\n", config_data[0].ratio);
    Usart1Printf("limit: %.2f\n", config_data[0].limit);
}

static void Cmd_Limit(char *buffer)
{
    float limit_input = 0.0f;
    int chars_read;

    int result = sscanf(buffer, "%f\n%n", &limit_input, &chars_read);
    if (result == 1 && chars_read == strlen(buffer))
    {
        if (limit_input >= 0.0f && limit_input <= 500.0f)
        {
            config_data[0].limit = limit_input;
            Usart1Printf("limit modified success\n");
            Usart1Printf("Limit = %.2f\n", config_data[0].limit);

            TF_WriteLog(LOG_LIMIT_CONFIG_OK);
        }
        else
        {
            Usart1Printf("limit invalid\n");
            Usart1Printf("Limit = %.2f\n", config_data[0].limit);
        }
    }
    else
    {
        Usart1Printf("limit invalid\n");
        Usart1Printf("Limit = %.2f\n", config_data[0].limit);
    }
}

void Cmd_GetRatio(void)
{
    Usart1Printf("report:ch0ratio =%.2f,ch1ratio =%.2f,ch2ratio =%.2f\r\n", 
                 config_data[0].ratio, config_data[1].ratio, config_data[2].ratio);
}

void Cmd_SetRatio(char *buffer)
{
    float ch0_input = 0.0f, ch1_input = 0.0f, ch2_input = 0.0f;
    int chars_read = 0;

    int result = sscanf(buffer, "command:set_ratio:ch0=%f,ch1=%f,ch2=%f\r\n%n", 
                        &ch0_input, &ch1_input, &ch2_input, &chars_read);
    if (result == 3 && chars_read == strlen(buffer))
    {
        if (TF_WriteRatio(ch0_input, ch1_input, ch2_input) == SUCCESS)
        {
            config_data[0].ratio = ch0_input; config_data[1].ratio = ch1_input; config_data[2].ratio = ch2_input;
            Usart1Printf("report:ok\r\n");
            TF_WriteLog(LOG_RATIO_CONFIG_OK);
        }
    }
}

static void Cmd_Conf(void)
{
    FIL config_file;
    FRESULT result = FR_OK;
    BYTE buffer[128] = {0};
    UINT bytes_read = 0;

    result = f_open(&config_file, "0:/config.ini", FA_OPEN_EXISTING | FA_READ);
    if (result != FR_OK)
    {
        Usart1Printf("config.ini file not found\n");
        return;
    }

    result = f_read(&config_file, buffer, sizeof(buffer), &bytes_read);
    if (result != FR_OK)
    {
        Usart1Printf("config.ini file not found\n");
        return;
    }
    f_close(&config_file);

    sscanf((char *)buffer, "[Ratio]\nCh0 = %f\n\n[Limit]\nCh0 = %f", &config_data[0].ratio, &config_data[0].limit);

    Flash_WriteConfig();

    Usart1Printf("Ratio = %.2f\n", config_data[0].ratio);
    Usart1Printf("Limit = %.2f\n", config_data[0].limit);
    Usart1Printf("config read success\n");
}

void Cmd_GetRtc(void)
{
    Usart1Printf("report:currentTime=20%hhu-%02hhu-%02hhu %02hhu:%02hhu:%02hhu\r\n",
                 ucRtc.year, ucRtc.month, ucRtc.date, ucRtc.hour, 
                 ucRtc.minute, ucRtc.second);
}

void Cmd_SetRtc(char *buffer)
{
    rtc_parameter_struct current_time = {0};
    int chars_read = 0;

    int result = sscanf(buffer, "command:set_RTC=20%hhu-%hhu-%hhu %hhu:%hhu:%hhu\r\n%n", 
                        &current_time.year, &current_time.month, &current_time.date, &current_time.hour, 
                        &current_time.minute, &current_time.second, &chars_read);
    if (result == 6 && chars_read == strlen(buffer))
    {
        if (SetRtc(&current_time) == SUCCESS)
        {
            Usart1Printf("report:ok\r\n");
            TF_WriteLog(LOG_RTC_CONFIG_OK);
        }
    }
}

static void Cmd_TestProc(void)
{
    ErrStatus test_status = SUCCESS;
    DSTATUS disk_status = 0;
    Usart1Printf("======system selftest======\n");

    if (flash_id == 0xc84013) Usart1Printf("flash............ok\n");
    else 
    {
        Usart1Printf("flash............error\n");
        test_status = ERROR;
        TF_WriteLog(LOG_FLASH_ERROR);
    }
    disk_status = disk_initialize(0);
    if (disk_status == 0) Usart1Printf("TF card...........ok\n");
    else 
    {
        Usart1Printf("TF card...........error\n");
        test_status = ERROR;
        TF_WriteLog(LOG_TFCARD_ERROR);
    }
    if (flash_id == 0xc84013) Usart1Printf("flash ID: 0x%lx\n", flash_id);
    else Usart1Printf("can not find flash\n");
    if (disk_status == 0) Usart1Printf("TF card memory: %uKB\n", sd_card_capacity_get());
    else Usart1Printf("can not find TF card\n");

    Usart1Printf("RTC: 20%02d-%02d-%02d %02d:%02d:%02d\n", ucRtc.year, ucRtc.month, ucRtc.date, ucRtc.hour, ucRtc.minute, ucRtc.second);
    Usart1Printf("======system selftest======\n");

    if (test_status == SUCCESS) TF_WriteLog(LOG_TEST_OK);
}

static void CmdProc(char *buffer)
{
    if (usart_status == LIMIT_CONFIG)
    {
        Cmd_Limit(buffer);
        usart_status = OTHER_CMD;
        return;
    }
    else if (usart_status == DEVICE_ID_CONFIG)
    {
        Cmd_DeviceIdConfig(buffer);
        usart_status = OTHER_CMD;
        return;
    }
    else
    {
        if (strcmp(buffer, "command:get_device_id\r\n") == 0) Cmd_GetDeviceId();
        else if (strcmp(buffer, "command:get_RTC\r\n") == 0) Cmd_GetRtc();
        else if (strncmp(buffer, "command:set_RTC", 15) == 0)
        {
            Cmd_SetRtc(buffer);
            TF_WriteLog(LOG_RTC_CONFIG);
        }
        else if (strncmp(buffer, "command:set_ratio", 17) == 0)
        {
            Cmd_SetRatio(buffer);
            TF_WriteLog(LOG_RATIO_CONFIG);
        }
        else if(strcmp(buffer, "command:get_ratio\r\n") == 0) Cmd_GetRatio();
        else if (strcmp(buffer, "test\n") == 0)
        {
            TF_WriteLog(LOG_SYSTEM_TEST);
            Cmd_TestProc();
        }
        else if (strcmp(buffer, "conf\n") == 0) Cmd_Conf();
        else if (strcmp(buffer, "limit\n") == 0)
        {
            Usart1Printf("Limit = %.2f\n", config_data[0].limit);
            Usart1Printf("Input value(0~500):\n");
            usart_status = LIMIT_CONFIG;

            TF_WriteLog(LOG_LIMIT_CONFIG);
        }
        else if (strcmp(buffer, "config save\n") == 0) Cmd_ConfigSave();
        else if (strcmp(buffer, "config read\n") == 0) Cmd_ConfigRead();
        else if (strcmp(buffer, "start\n") == 0) 
        {
            Cmd_Start();
            TF_WriteLog(LOG_SAMPLE_START_USART);
        }
        else if (strcmp(buffer, "stop\n") == 0)
        {
            Cmd_Stop();
            TF_WriteLog(LOG_SAMPLE_STOP_USART);
        }
        else if (strcmp(buffer, "hide\n") == 0)
        {
            Cmd_Hide();
            TF_WriteLog(LOG_HIDE_DATA);
        }
        else if (strcmp(buffer, "unhide\n") == 0) 
        {
            Cmd_UnHide();
            TF_WriteLog(LOG_UNHIDE_DATA);
        }
        else if (strcmp(buffer, "power reset\n") == 0) Cmd_PowerReset();
        else if (strcmp(buffer, "device_id\n") == 0)
        {
            Usart1Printf("device_id:\n");
            usart_status = DEVICE_ID_CONFIG;
        }
    }
}

void Usart0Task(void)
{
    if (usart0_rx_flag == 0) return;

    CmdProc((char *)usart0_rx_buffer_proc);

    memset(usart0_rx_buffer_proc, 0, USART0_BUFFER_SIZE);
    usart0_rx_flag = 0;
}

void Usart1Task(void)
{
    if (usart1_rx_flag == 0) return;
    
    CmdProc((char *)usart1_rx_buffer_proc);

    memset(usart1_rx_buffer_proc, 0, USART1_BUFFER_SIZE);
    usart1_rx_flag = 0;
}
