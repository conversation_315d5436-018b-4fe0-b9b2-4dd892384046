/*!
    \file    main.h
    \brief   the header file of main

    \version 2024-12-20, V3.3.1, firmware for GD32F4xx
*/

/*
    Copyright (c) 2024, GigaDevice Semiconductor Inc.

    Redistribution and use in source and binary forms, with or without modification,
are permitted provided that the following conditions are met:

    1. Redistributions of source code must retain the above copyright notice, this
       list of conditions and the following disclaimer.
    2. Redistributions in binary form must reproduce the above copyright notice,
       this list of conditions and the following disclaimer in the documentation
       and/or other materials provided with the distribution.
    3. Neither the name of the copyright holder nor the names of its contributors
       may be used to endorse or promote products derived from this software without
       specific prior written permission.

    THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY
OF SUCH DAMAGE.
*/

#ifndef __MAIN_H
#define __MAIN_H

typedef struct
{
    float ratio;
    float limit;
} Config_t;

typedef struct
{
    uint8_t enable;
    uint8_t last_sample_time;
    uint8_t hide;
} SampleStatus_t;

typedef struct
{
    uint8_t sample_cmd_count;
    char sample_file_name[64];
    uint8_t sample_file_open;
    uint8_t overLimit_cmd_count;
    char overLimit_file_name[64];
    uint8_t overLimit_file_open;
    uint8_t hide_cmd_count;
    char hide_file_name[64];
    uint8_t hide_file_open;
    char log_file_name[64];
    uint8_t log_file_open;
} Storage_t;

extern Config_t config_data[3];
extern SampleStatus_t sample_status;
extern Storage_t storage_param;
extern volatile uint8_t sample_cycle;
extern uint16_t power_count;
extern uint8_t device_id[32];

#endif /* __MAIN_H */


