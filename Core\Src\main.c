#include "gd32f470vet6_bsp.h"

Config_t config_data[3] =
{
    {1.99f, 10.11f},
    {2.99f, 20.11f},
    {3.99f, 30.11f}
};

SampleStatus_t sample_status = 
{
    .enable = 0,
    .last_sample_time = 0,
    .hide = 0
};

Storage_t storage_param =
{
    .sample_cmd_count = 0,
    .sample_file_name = {0},
    .sample_file_open = 0,
    .overLimit_cmd_count = 0,
    .overLimit_file_name = {0},
    .overLimit_file_open = 0,
    .hide_cmd_count = 0,
    .hide_file_name = {0},
    .hide_file_open = 0,
    .log_file_name = {0},
    .log_file_open = 0
};

volatile uint8_t sample_cycle = 5;
uint16_t power_count = 0;

uint8_t device_id[32] = {0};

static void ConfigInit(void)
{
    DSTATUS disk_status = 0;

    Flash_ReadConfig();
    Flash_ReadCycle();

    OLED_Clear();
    OledDrawStr(0, 0, "system idle");

    f_mkdir("0:/hideData");
    f_mkdir("0:/log");
    f_mkdir("0:/overLimit");
    f_mkdir("0:/sample");

    Flash_ReadPower();

    disk_status = disk_initialize(0);
    if (disk_status == 0)
    {
        power_count++;
        Flash_WritePower();
        power_count--;
    }
    
    // Usart1Printf("power_count: %d\n", power_count);

    RtcTask();
    TF_WriteLog(LOG_SYSTEM_INIT);

    Usart1Printf("====system init====\n");
    Flash_ReadDeviceId();
    Usart1Printf("Device_ID:2025-CIMC-%s\n", device_id);
    Usart1Printf("====system ready====\n");
}

int main(void)
{
    SysInit();
	
    ConfigInit();

    while(1)
    {
        TaskExeution();
    }
}
