#include "rtc_app.h"

void RtcTask(void)
{
    ReadRtc(&ucRtc);
}

time_t HideTimeProc(void)
{
    struct tm time_info = {0};
    time_t unix_timestamp;

    time_info.tm_year = ucRtc.year + 2000 - 1900;
    time_info.tm_mon = ucRtc.month - 1;
    time_info.tm_mday = ucRtc.date;
    time_info.tm_hour = ucRtc.hour;
    time_info.tm_min = ucRtc.minute;
    time_info.tm_sec = ucRtc.second;
    time_info.tm_isdst = 0;

    unix_timestamp = mktime(&time_info);
    unix_timestamp -= 28800;

    return unix_timestamp;
}
